from rest_framework.views import APIView
import pandas as pd
from google.cloud import bigquery
from rest_framework import serializers
from rest_framework.response import Response

from picking.models import PickOrder


# Field name and type mappings
ORDER_ITEMS_FIELDS = {
    "id": "STRING",
    "sku": "STRING",
    "name": "STRING",
    "number": "FLOAT",
    "status": "STRING",
    "skutype": "INTEGER",
    "discount": "STRING",
    "mfg_date": "INTEGER",
    "unittext": "STRING",
    "productid": "INTEGER",
    "totalprice": "FLOAT",
    "producttype": "INTEGER",
    "sku_barcode": "STRING",
    "serialnolist": "INTEGER",  # REPEATED
    "discountamount": "FLOAT",
    "eso_vatpercent": "FLOAT",
    "original_price": "FLOAT",
    "pricepernumber": "FLOAT",
    "return_quantity": "INTEGER",
    "seller_discount": "FLOAT",
    "shipping_amount": "FLOAT",
    "platform_discount": "FLOAT",
    "original_shipping_amount": "FLOAT",
    "seller_shipping_discount": "FLOAT",
    "platform_shipping_discount": "FLOAT",
    "order_id": "INTEGER",
}

ORDERS_FIELDS = {
    "id": "STRING",
    "tag": "INTEGER",  # REPEATED
    "line": "INTEGER",
    "isCOD": "BOOLEAN",
    "amount": "FLOAT",
    "freeze": "INTEGER",
    "lineid": "STRING",
    "number": "STRING",
    "remark": "STRING",
    "status": "STRING",
    "vattype": "INTEGER",
    "version": "INTEGER",
    "discount": "STRING",
    "createdby": "INTEGER",
    "ordertype": "INTEGER",
    "reference": "STRING",
    "sharelink": "INTEGER",
    "vatamount": "FLOAT",
    "customerid": "STRING",
    "facebookid": "STRING",
    "trackingno": "STRING",
    "vatpercent": "FLOAT",
    "description": "STRING",
    "shippingvat": "INTEGER",
    "createuserid": "INTEGER",
    "customercode": "STRING",
    "customername": "STRING",
    "facebookname": "INTEGER",
    "saleschannel": "STRING",
    "shippingname": "STRING",
    "amount_pretax": "INTEGER",
    "customeremail": "STRING",
    "customerphone": "STRING",
    "paymentamount": "FLOAT",
    "paymentmethod": "STRING",
    "paymentstatus": "STRING",
    "remark_status": "STRING",
    "return_status": "STRING",
    "shippingemail": "STRING",
    "shippingphone": "STRING",
    "voucheramount": "FLOAT",
    "warehousecode": "STRING",
    "createusername": "STRING",
    "discountamount": "FLOAT",
    "sellerdiscount": "FLOAT",
    "shippingamount": "FLOAT",
    "customeraddress": "STRING",
    "discount_amount": "FLOAT",
    "integrationName": "STRING",
    "integrationShop": "STRING",
    "marketplacename": "INTEGER",
    "orderdateString": "STRING",
    "platform_status": "STRING",
    "seller_discount": "FLOAT",
    "shipping_amount": "FLOAT",
    "shippingaddress": "STRING",
    "shippingchannel": "STRING",
    "customerbranchno": "STRING",
    "customeridnumber": "STRING",
    "customerpostcode": "STRING",
    "expireDateString": "INTEGER",
    "platformdiscount": "FLOAT",
    "shippingdistrict": "STRING",
    "shippingpostcode": "STRING",
    "shippingprovince": "STRING",
    "platform_discount": "FLOAT",
    "customerbranchname": "STRING",
    "marketplacepayment": "INTEGER",
    "shippingdateString": "INTEGER",
    "is_confirm_received": "BOOLEAN",
    "shipping_sorting_no": "STRING",
    "shippingsubdistrict": "STRING",
    "createdatetimeString": "STRING",
    "updatedatetimeString": "STRING",
    "orderManagementSystem": "STRING",
    "paymentdatetimeString": "INTEGER",
    "original_shipping_amount": "FLOAT",
    "seller_shipping_discount": "FLOAT",
    "marketplaceshippingstatus": "INTEGER",
    "platform_shipping_discount": "FLOAT",
    "source_id": "INTEGER",
}


# Special field configurations
REPEATED_FIELDS = {
    "serialnolist": "INTEGER",  # for ORDER_ITEMS
    "tag": "INTEGER",  # for ORDERS
}

# Type mapping from BigQuery types to pandas types
BQ_TO_PANDAS_TYPE_MAP = {
    "INTEGER": "Int64",
    "FLOAT": "float64",
    "STRING": "string",
    "BOOLEAN": "boolean",
}


# Helper functions to generate schemas
def create_bigquery_schema(field_mapping, repeated_fields=None):
    """Generate BigQuery schema from field mapping."""
    if repeated_fields is None:
        repeated_fields = {}

    schema = []
    for field_name, bq_type in field_mapping.items():
        if field_name in repeated_fields:
            mode = "REPEATED"
        else:
            mode = "NULLABLE"
        schema.append(bigquery.SchemaField(field_name, bq_type, mode=mode))
    return schema


def create_pandas_schema_mapping(field_mapping, repeated_fields=None):
    """Generate pandas data type mapping from field mapping."""
    if repeated_fields is None:
        repeated_fields = {}

    pandas_mapping = {}
    for field_name, bq_type in field_mapping.items():
        if field_name in repeated_fields:
            pandas_mapping[field_name] = "object"  # Special handling for REPEATED
        else:
            pandas_mapping[field_name] = BQ_TO_PANDAS_TYPE_MAP.get(bq_type, "object")
    return pandas_mapping


# Generated schemas and mappings
ORDER_ITEMS_SCHEMA = create_bigquery_schema(
    ORDER_ITEMS_FIELDS, {"serialnolist": "INTEGER"}
)
ORDERS_SCHEMA = create_bigquery_schema(ORDERS_FIELDS, {"tag": "INTEGER"})
ORDER_ITEMS_SCHEMA_MAPPING = create_pandas_schema_mapping(
    ORDER_ITEMS_FIELDS, {"serialnolist": "INTEGER"}
)
ORDERS_SCHEMA_MAPPING = create_pandas_schema_mapping(ORDERS_FIELDS, {"tag": "INTEGER"})


def convert_repeated_field_to_int_list(val):
    """
    Convert a value to a list of integers for BigQuery REPEATED fields.
    Handles various input formats including JSON strings and existing lists.
    """
    if pd.isna(val) or val is None:
        return []
    if isinstance(val, list):
        return [int(x) for x in val if pd.notna(x)]
    if isinstance(val, str):
        try:
            # Try to parse as JSON array
            import json

            parsed = json.loads(val)
            if isinstance(parsed, list):
                return [int(x) for x in parsed if pd.notna(x)]
        except (json.JSONDecodeError, ValueError, TypeError):
            pass
    return []


def prepare_dataframe_for_bigquery(
    df, schema_mapping, repeated_fields=None, special_defaults=None
):
    """
    Generic function to prepare DataFrame to match BigQuery schema.
    Ensures data types and handles missing columns.

    Args:
        df: Input DataFrame
        schema_mapping: Dictionary mapping column names to pandas data types
        repeated_fields: List of column names that are REPEATED fields in BigQuery
        special_defaults: Dictionary of column names to default values
    """
    # Create a copy to avoid modifying the original
    df_prepared = df.copy()

    if repeated_fields is None:
        repeated_fields = []
    if special_defaults is None:
        special_defaults = {}

    # Add missing columns with None values
    for col in schema_mapping.keys():
        if col not in df_prepared.columns:
            df_prepared[col] = None

    # Convert data types
    for col, dtype in schema_mapping.items():
        if col in df_prepared.columns:
            try:
                if dtype == "string":
                    df_prepared[col] = df_prepared[col].astype("string")
                elif dtype == "Int64":
                    df_prepared[col] = pd.to_numeric(
                        df_prepared[col], errors="coerce"
                    ).astype("Int64")
                elif dtype == "float64":
                    df_prepared[col] = pd.to_numeric(
                        df_prepared[col], errors="coerce"
                    ).astype("float64")
                elif dtype == "boolean":
                    df_prepared[col] = df_prepared[col].astype("boolean")
                elif col in repeated_fields:
                    # Handle REPEATED fields
                    df_prepared[col] = df_prepared[col].apply(
                        convert_repeated_field_to_int_list
                    )
            except Exception as e:
                print(f"Warning: Could not convert column {col} to {dtype}: {e}")

    # Apply special defaults
    for col, default_value in special_defaults.items():
        if col in df_prepared.columns:
            df_prepared[col] = df_prepared[col].fillna(default_value)

    # Ensure we only have the columns defined in the schema
    df_prepared = df_prepared[list(schema_mapping.keys())]

    return df_prepared


def prepare_order_items_data(df_items):
    """
    Prepare order items DataFrame to match BigQuery schema.
    """
    return prepare_dataframe_for_bigquery(
        df_items,
        ORDER_ITEMS_SCHEMA_MAPPING,
        repeated_fields=["serialnolist"],
        special_defaults={"return_quantity": 0.0},
    )


def prepare_orders_data(df_orders):
    """
    Prepare orders DataFrame to match BigQuery schema.
    """
    return prepare_dataframe_for_bigquery(
        df_orders, ORDERS_SCHEMA_MAPPING, repeated_fields=["tag"]
    )


def generate_merge_sql(target_table, staging_table, schema_mapping, match_conditions):
    """
    Generate BigQuery MERGE SQL statement dynamically based on schema mapping.

    Args:
        target_table: Target table name
        staging_table: Staging table name
        schema_mapping: Dictionary mapping column names to data types
        match_conditions: List of column names to use for matching records
    """
    columns = list(schema_mapping.keys())

    # Generate column assignments for UPDATE
    update_assignments = [f"target.{col} = src.{col}" for col in columns]
    update_clause = ",\n            ".join(update_assignments)

    # Generate column lists for INSERT
    insert_columns = ", ".join(columns)
    insert_values = ", ".join([f"src.{col}" for col in columns])

    # Generate match conditions
    match_clause = " AND ".join(
        [f"target.{col} = src.{col}" for col in match_conditions]
    )

    merge_sql = f"""
        MERGE `{target_table}` AS target
        USING `{staging_table}` AS src
        ON {match_clause}
        WHEN MATCHED THEN
        UPDATE SET
            {update_clause}
        WHEN NOT MATCHED THEN
        INSERT (
            {insert_columns}
        ) VALUES (
            {insert_values}
        )
        """

    return merge_sql


def load_dataframe_to_bigquery(
    client,
    df,
    table_name,
    schema,
    write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
):
    """
    Load DataFrame to BigQuery table with specified schema.

    Args:
        client: BigQuery client
        df: DataFrame to load
        table_name: Target table name
        schema: BigQuery schema
        write_disposition: Write disposition (default: WRITE_TRUNCATE)
    """
    job_config = bigquery.LoadJobConfig(
        write_disposition=write_disposition,
        schema=schema,
    )
    load_job = client.load_table_from_dataframe(df, table_name, job_config=job_config)
    load_job.result()
    return load_job


class BigQueryOrderImportAPI(APIView):
    authentication_classes = []
    permission_classes = []

    class Validator(serializers.Serializer):
        start_date = serializers.DateTimeField()
        end_date = serializers.DateTimeField()
        company = serializers.IntegerField(allow_null=True, required=False)

    def post(self, request):
        """
        Handle POST requests to import orders into BigQuery.
        """
        validator = self.Validator(data=request.data)
        validator.is_valid(raise_exception=True)
        data = validator.validated_data
        start_date = data["start_date"]
        end_date = data["end_date"]
        company = data.get("company")

        # BigQuery setup
        BQ_DATASET = "dobybot.report_api_tester"
        STG_ORDERS_TABLE = f"{BQ_DATASET}.staging_orders"
        STG_ITEMS_TABLE = f"{BQ_DATASET}.staging_order_items"
        ORDERS_TABLE = f"{BQ_DATASET}.orders"
        ITEMS_TABLE = f"{BQ_DATASET}.order_items"
        bq_client = bigquery.Client()

        # 1. fetch raw JSON rows
        pick_orders = PickOrder.objects.filter(
            update_date__gte=start_date,
            update_date__lte=end_date,
        )

        if company:
            pick_orders = pick_orders.filter(company=company)

        orders_list = []
        items_list = []

        # 2. deserialize and flatten
        for rec in pick_orders.values("id", "order_json"):
            od = rec["order_json"]
            # extract top-level order fields
            order_record = {
                k: od[k] for k in od if k not in ("list", "payments", "extra")
            }
            order_record["source_id"] = rec["id"]
            orders_list.append(order_record)

            # extract line items
            for item in od["list"]:
                item_flat = item.copy()
                item_flat["id"] = (
                    f'{od["number"]}-{rec.get("id", "no_id")}-{item.get("sku", "no_sku")}-{item.get("productid", "no_productid")}'
                )
                item_flat["order_id"] = od["number"]
                items_list.append(item_flat)

        df_orders = pd.DataFrame(orders_list)
        df_items = pd.DataFrame(items_list)

        # 2. Load into staging tables, truncating them:
        # Prepare and load orders data
        df_orders_prepared = prepare_orders_data(df_orders)
        load_dataframe_to_bigquery(
            bq_client, df_orders_prepared, STG_ORDERS_TABLE, ORDERS_SCHEMA
        )

        # Prepare and load items data
        df_items_prepared = prepare_order_items_data(df_items)
        load_dataframe_to_bigquery(
            bq_client, df_items_prepared, STG_ITEMS_TABLE, ORDER_ITEMS_SCHEMA
        )

        # 3. MERGE staging → production (dedupe on order_id/[item_id]):
        merge_orders_sql = generate_merge_sql(
            ORDERS_TABLE, STG_ORDERS_TABLE, ORDERS_SCHEMA_MAPPING, ["number"]
        )

        merge_items_sql = generate_merge_sql(
            ITEMS_TABLE,
            STG_ITEMS_TABLE,
            ORDER_ITEMS_SCHEMA_MAPPING,
            ["id"],
        )

        bq_client.query(merge_orders_sql).result()
        bq_client.query(merge_items_sql).result()

        return Response(
            {
                "status": "success",
                "message": f"Loaded {len(df_orders_prepared)} orders & {len(df_items_prepared)} items with schema enforcement.",
            },
            status=200,
        )
